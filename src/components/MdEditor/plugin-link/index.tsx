/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable indent */
import { expectDomTypeError } from '@milkdown/exception';
import { toggleMark } from '@milkdown/prose/commands';
import type { Node as ProseNode } from '@milkdown/prose/model';
import { TextSelection } from '@milkdown/prose/state';
import { $command, $markSchema } from '@milkdown/utils';
import type { Meta, MilkdownPlugin } from '@milkdown/ctx';
import { linkAttr } from '@milkdown/preset-commonmark';

export function withMeta<T extends MilkdownPlugin>(
  plugin: T,
  meta: Partial<Meta> & Pick<Meta, 'displayName'>
): T {
  Object.assign(plugin, {
    meta: {
      package: '@milkdown/preset-commonmark',
      ...meta,
    },
  });

  return plugin;
}

withMeta(linkAttr, {
  displayName: 'Attr<link>',
  group: 'Link',
});

/// Link mark schema.
export const linkSchema = $markSchema('link', (ctx) => {
  const schema = {
    attrs: {
      href: {},
      title: { default: null },
      target: { default: '_blank' },
    },
    inclusive: false,
    parseDOM: [
      {
        tag: 'a[href]',
        getAttrs: (dom: any) => {
          if (!(dom instanceof HTMLElement)) {
            throw expectDomTypeError(dom);
          }
          return {
            href: dom.getAttribute('href'),
            title: dom.getAttribute('title'),
            target: dom.getAttribute('target') || '_blank',
          };
        },
      },
    ],
    toDOM: (mark: any) => {
      const attrs = { ...ctx.get(linkAttr.key)(mark), ...mark.attrs };

      // 提取href值
      const { href } = attrs;

      if (href) {
        // 移除中文字符，只保留链接部分
        const linkOnly = href.replace(
          /[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff\u3040-\u309f\u30a0-\u30ff]/g,
          ''
        );

        // 如果处理后还有有效的链接内容，使用a标签，href使用纯净链接
        if (
          linkOnly.trim() &&
          (linkOnly.includes('http') ||
            linkOnly.includes('www') ||
            linkOnly.includes('.'))
        ) {
          // 创建a标签，href使用纯净的链接，但保留其他属性
          const linkAttrs = {
            ...attrs,
            href: linkOnly.trim(),
            target: attrs.target || '_blank',
          };
          return ['a', linkAttrs] as const;
        }
      }

      // 其他情况使用span标签
      return ['span', attrs] as const;
    },
    parseMarkdown: {
      match: (node: any) => node.type === 'link',
      runner: (state: any, node: any, markType: any) => {
        const url = node.url as string;
        const title = node.title as string;
        const target = (node.target as string) || '_blank';
        state.openMark(markType, { href: url, title, target });
        state.next(node.children);
        state.closeMark(markType);
      },
    },
    toMarkdown: {
      match: (mark: any) => mark.type.name === 'link',
      runner: (state: any, mark: any) => {
        state.withMark(mark, 'link', undefined, {
          title: mark.attrs.title,
          url: mark.attrs.href,
          target: mark.attrs.target,
        });
      },
    },
  };
  return schema;
});

withMeta(linkSchema.mark, {
  displayName: 'MarkSchema<link>',
  group: 'Link',
});

/// @internal
export interface UpdateLinkCommandPayload {
  href?: string;
  title?: string;
  target?: string;
}
/// A command to toggle the link mark.
/// You can pass the `href` and `title` to the link.
export const toggleLinkCommand = $command(
  'ToggleLink',
  (ctx) =>
    (payload: UpdateLinkCommandPayload = {}) =>
      toggleMark(linkSchema.type(ctx), payload)
);

withMeta(toggleLinkCommand, {
  displayName: 'Command<toggleLinkCommand>',
  group: 'Link',
});

/// A command to update the link mark.
/// You can pass the `href` and `title` to update the link.
export const updateLinkCommand = $command(
  'UpdateLink',
  (ctx) =>
    (payload: UpdateLinkCommandPayload = {}) =>
    (state, dispatch) => {
      if (!dispatch) {
        return false;
      }

      let node: ProseNode | undefined;
      let pos = -1;
      const { selection } = state;
      const { from, to } = selection;
      state.doc.nodesBetween(from, from === to ? to + 1 : to, (n, p) => {
        if (linkSchema.type(ctx).isInSet(n.marks)) {
          node = n;
          pos = p;
          return false;
        }

        return undefined;
      });

      if (!node) {
        return false;
      }

      const mark = node.marks.find(({ type }) => type === linkSchema.type(ctx));
      if (!mark) {
        return false;
      }

      const start = pos;
      const end = pos + node.nodeSize;
      const { tr } = state;
      const linkMark = linkSchema
        .type(ctx)
        .create({ ...mark.attrs, ...payload });
      if (!linkMark) {
        return false;
      }

      dispatch(
        tr
          .removeMark(start, end, mark)
          .addMark(start, end, linkMark)
          .setSelection(new TextSelection(tr.selection.$anchor))
          .scrollIntoView()
      );

      return true;
    }
);

withMeta(updateLinkCommand, {
  displayName: 'Command<updateLinkCommand>',
  group: 'Link',
});
